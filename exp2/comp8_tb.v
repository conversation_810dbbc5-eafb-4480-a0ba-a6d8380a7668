/*//设计一个 字节（8位）的比较器
module comp8(
        input [7:0] a,
        input [7:0] b,
        output gt,
);
//a>b输出高电平
        assign gt = a>b;
endmodule
*/
//testbench
module comp8_tb();
reg [7:0] a;
reg [7:0] b;
wire gt;
comp8 uut(.a(a),.b(b),.gt(gt));
initial begin
	$dumpfile("comp8.vcd");
	$dumpvars(0,comp8_tb);
	a = 8'd0;
	b = 8'd0;
	#10;

	a = 8'd1;
	b = 8'd0;
	#10;

	a = 8'd0;
	b = 8'd1;
	#10;

	a = 8'd1;
	b = 8'd1;
end
endmodule
