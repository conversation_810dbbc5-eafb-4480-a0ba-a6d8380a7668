$date
	Wed Sep  3 14:32:24 2025
$end
$version
	Icarus Verilog
$end
$timescale
	1s
$end
$scope module comp8_tb $end
$var wire 1 ! gt $end
$var reg 8 " a [7:0] $end
$var reg 8 # b [7:0] $end
$scope module uut $end
$var wire 8 $ a [7:0] $end
$var wire 8 % b [7:0] $end
$var wire 1 ! gt $end
$upscope $end
$upscope $end
$enddefinitions $end
$comment Show the parameter values. $end
$dumpall
$end
#0
$dumpvars
b0 %
b0 $
b0 #
b0 "
0!
$end
#10
1!
b1 "
b1 $
#20
b1 #
b1 %
0!
b0 "
b0 $
#30
b1 "
b1 $
