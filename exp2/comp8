#! /usr/bin/vvp
:ivl_version "12.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
S_0x5739cf8984c0 .scope module, "comp8_tb" "comp8_tb" 2 12;
 .timescale 0 0;
v0x5739cf8e2ea0_0 .var "a", 7 0;
v0x5739cf8e2f90_0 .var "b", 7 0;
v0x5739cf8e3060_0 .net "gt", 0 0, L_0x5739cf8e3160;  1 drivers
S_0x5739cf898650 .scope module, "uut" "comp8" 2 16, 3 2 0, S_0x5739cf8984c0;
 .timescale 0 0;
    .port_info 0 /INPUT 8 "a";
    .port_info 1 /INPUT 8 "b";
    .port_info 2 /OUTPUT 1 "gt";
v0x5739cf8d2c20_0 .net "a", 7 0, v0x5739cf8e2ea0_0;  1 drivers
v0x5739cf8e2ca0_0 .net "b", 7 0, v0x5739cf8e2f90_0;  1 drivers
v0x5739cf8e2d80_0 .net "gt", 0 0, L_0x5739cf8e3160;  alias, 1 drivers
L_0x5739cf8e3160 .cmp/gt 8, v0x5739cf8e2ea0_0, v0x5739cf8e2f90_0;
    .scope S_0x5739cf8984c0;
T_0 ;
    %vpi_call 2 18 "$dumpfile", "comp8.vcd" {0 0 0};
    %vpi_call 2 19 "$dumpvars", 32'sb00000000000000000000000000000000, S_0x5739cf8984c0 {0 0 0};
    %pushi/vec4 0, 0, 8;
    %store/vec4 v0x5739cf8e2ea0_0, 0, 8;
    %pushi/vec4 0, 0, 8;
    %store/vec4 v0x5739cf8e2f90_0, 0, 8;
    %delay 10, 0;
    %pushi/vec4 1, 0, 8;
    %store/vec4 v0x5739cf8e2ea0_0, 0, 8;
    %pushi/vec4 0, 0, 8;
    %store/vec4 v0x5739cf8e2f90_0, 0, 8;
    %delay 10, 0;
    %pushi/vec4 0, 0, 8;
    %store/vec4 v0x5739cf8e2ea0_0, 0, 8;
    %pushi/vec4 1, 0, 8;
    %store/vec4 v0x5739cf8e2f90_0, 0, 8;
    %delay 10, 0;
    %pushi/vec4 1, 0, 8;
    %store/vec4 v0x5739cf8e2ea0_0, 0, 8;
    %pushi/vec4 1, 0, 8;
    %store/vec4 v0x5739cf8e2f90_0, 0, 8;
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 4;
    "N/A";
    "<interactive>";
    "comp8_tb.v";
    "comp8.v";
