`timescale 1ns / 1ps
`include "adder8.v"

module adder8_tb;

reg [7:0] a, b;
reg cin;
wire [7:0] sum;
wire cout;

reg [8:0] exp;
interger fails;
adder dut(.a(a), .b(b), .cin(cin), .sum(sum), .cout(cout));

initial begin
	$dumpfile("adder8_tb.vcd");
	$dumpvars(0, adder8_tb);

	fails = 0;
	a=8'h00; b=8'h00; cin=0; #1,exp=a+b+cin;
	if({cout,sum} !== exp) begin $display("FAIL 1"); fails=fails+1; end

	a=8'h01; b=8'h01; cin=0; #1,exp=a+b+cin;
	if({cout,sum} !== exp) begin $display("FAIL 2"); fails=fails+1; end
	
	a=8'hFF; b=8'h01; cin=0; #1,exp=a+b+cin;
	if({cout,sum} !== exp) begin $display("FAIL 3"); fails=fails+1; end
	
	a=8'hAA; b=8'h55; cin=0; #1,exp=a+b+cin;
	if({cout,sum} !== exp) begin $display("FAIL 4"); fails=fails+1; end
	
	a=8'h80; b=8'h80; cin=0; #1,exp=a+b+cin;
	if({cout,sum} !== exp) begin $display("FAIL 5"); fails=fails+1; end

	a=8'hC8; b=8'h64; cin=1; #1,exp=a+b+cin;
	if({cout,sum} !== exp) begin $display("FAIL 6"); fails=fails+1; end
	
	a=8'hFF; b=8'hFF; cin=1; #1,exp=a+b+cin;
	if({cout,sum} !== exp) begin $display("FAIL 7"); fails=fails+1; end
	
	if(fails == 0) $display("tests PASS");
	else $display("%0d simple tests FAILED", fails);
	#5 $finish;
end

endmodule

