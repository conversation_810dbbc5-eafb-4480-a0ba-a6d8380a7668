module full_adder_tb;
reg A,B,Cin;
wire S,Cout;

full_adder uut (
    .A(A),
    .B(B),
    .Cin(Cin),
    .S(S),
    .Cout(Cout)
);

initial begin
     A=0;B=0;Cin=0;#10;
     A=0;B=0;Cin=1;#10;
     A=0;B=1;Cin=0;#10;
     A=0;B=1;Cin=1;#10;
     A=1;B=0;Cin=0;#10;
     A=1;B=0;Cin=1;#10;
     A=1;B=1;Cin=0;#10;
     A=1;B=1;Cin=1;#10;
    $finish;
end

initial begin
    $dumpfile("full_adder_wave.vcd");
    $dumpvars(0,full_adder_tb);
end
endmodule