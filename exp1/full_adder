#! /usr/bin/vvp
:ivl_version "12.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
S_0x57487dbe5310 .scope module, "full_adder_tb" "full_adder_tb" 2 1;
 .timescale 0 0;
v0x57487dbf9470_0 .var "A", 0 0;
v0x57487dbf9530_0 .var "B", 0 0;
v0x57487dbf9600_0 .var "Cin", 0 0;
v0x57487dbf9700_0 .net "Cout", 0 0, L_0x57487dbf9f40;  1 drivers
v0x57487dbf97d0_0 .net "S", 0 0, L_0x57487dbf99b0;  1 drivers
S_0x57487dbe54a0 .scope module, "uut" "full_adder" 2 5, 3 1 0, S_0x57487dbe5310;
 .timescale 0 0;
    .port_info 0 /INPUT 1 "A";
    .port_info 1 /INPUT 1 "B";
    .port_info 2 /INPUT 1 "Cin";
    .port_info 3 /OUTPUT 1 "S";
    .port_info 4 /OUTPUT 1 "Cout";
L_0x57487dbf9870 .functor XOR 1, v0x57487dbf9470_0, v0x57487dbf9530_0, C4<0>, C4<0>;
L_0x57487dbf99b0 .functor XOR 1, L_0x57487dbf9870, v0x57487dbf9600_0, C4<0>, C4<0>;
L_0x57487dbf9b10 .functor AND 1, v0x57487dbf9470_0, v0x57487dbf9530_0, C4<1>, C4<1>;
L_0x57487dbf9b80 .functor AND 1, v0x57487dbf9470_0, v0x57487dbf9600_0, C4<1>, C4<1>;
L_0x57487dbf9cb0 .functor OR 1, L_0x57487dbf9b10, L_0x57487dbf9b80, C4<0>, C4<0>;
L_0x57487dbf9d70 .functor AND 1, v0x57487dbf9530_0, v0x57487dbf9600_0, C4<1>, C4<1>;
L_0x57487dbf9f40 .functor OR 1, L_0x57487dbf9cb0, L_0x57487dbf9d70, C4<0>, C4<0>;
v0x57487dbe5680_0 .net "A", 0 0, v0x57487dbf9470_0;  1 drivers
v0x57487dbf8c40_0 .net "B", 0 0, v0x57487dbf9530_0;  1 drivers
v0x57487dbf8d00_0 .net "Cin", 0 0, v0x57487dbf9600_0;  1 drivers
v0x57487dbf8da0_0 .net "Cout", 0 0, L_0x57487dbf9f40;  alias, 1 drivers
v0x57487dbf8e60_0 .net "S", 0 0, L_0x57487dbf99b0;  alias, 1 drivers
v0x57487dbf8f70_0 .net *"_ivl_0", 0 0, L_0x57487dbf9870;  1 drivers
v0x57487dbf9050_0 .net *"_ivl_10", 0 0, L_0x57487dbf9d70;  1 drivers
v0x57487dbf9130_0 .net *"_ivl_4", 0 0, L_0x57487dbf9b10;  1 drivers
v0x57487dbf9210_0 .net *"_ivl_6", 0 0, L_0x57487dbf9b80;  1 drivers
v0x57487dbf92f0_0 .net *"_ivl_8", 0 0, L_0x57487dbf9cb0;  1 drivers
    .scope S_0x57487dbe5310;
T_0 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9470_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9530_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x57487dbf9600_0, 0, 1;
    %delay 10, 0;
    %vpi_call 2 22 "$finish" {0 0 0};
    %end;
    .thread T_0;
    .scope S_0x57487dbe5310;
T_1 ;
    %vpi_call 2 26 "$dumpfile", "full_adder_wave.vcd" {0 0 0};
    %vpi_call 2 27 "$dumpvars", 32'sb00000000000000000000000000000000, S_0x57487dbe5310 {0 0 0};
    %end;
    .thread T_1;
# The file index is used to find the file name in the following table.
:file_names 4;
    "N/A";
    "<interactive>";
    "full_adder_tb.v";
    "full_adder.v";
