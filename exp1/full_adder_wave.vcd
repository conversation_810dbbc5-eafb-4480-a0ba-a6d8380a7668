$date
	Mon Sep  1 18:30:52 2025
$end
$version
	Icarus Verilog
$end
$timescale
	1s
$end
$scope module full_adder_tb $end
$var wire 1 ! S $end
$var wire 1 " Cout $end
$var reg 1 # A $end
$var reg 1 $ B $end
$var reg 1 % Cin $end
$scope module uut $end
$var wire 1 # A $end
$var wire 1 $ B $end
$var wire 1 % Cin $end
$var wire 1 " Cout $end
$var wire 1 ! S $end
$upscope $end
$upscope $end
$enddefinitions $end
$comment Show the parameter values. $end
$dumpall
$end
#0
$dumpvars
0%
0$
0#
0"
0!
$end
#10
1!
1%
#20
0%
1$
#30
1"
0!
1%
#40
0"
1!
0%
0$
1#
#50
1"
0!
1%
#60
0%
1$
#70
1!
1%
#80
